/**
  ******************************************************************************
  * @file    font20.c
  * <AUTHOR> Application Team
  * @version V1.0.0
  * @date    18-February-2014
  * @brief   This file provides text font20 for STM32xx-EVAL's LCD driver. 
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; COPYRIGHT(c) 2014 STMicroelectronics</center></h2>
  *
  * Redistribution and use in source and binary forms, with or without modification,
  * are permitted provided that the following conditions are met:
  *   1. Redistributions of source code must retain the above copyright notice,
  *      this list of conditions and the following disclaimer.
  *   2. Redistributions in binary form must reproduce the above copyright notice,
  *      this list of conditions and the following disclaimer in the documentation
  *      and/or other materials provided with the distribution.
  *   3. Neither the name of STMicroelectronics nor the names of its contributors
  *      may be used to endorse or promote products derived from this software
  *      without specific prior written permission.
  *
  * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
  * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
  * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
  * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
  * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
  * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
  * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
  * CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
  * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
  * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "fonts.h"

/** @addtogroup Utilities
  * @{
  */
  
/** @addtogroup STM32_EVAL
  * @{
  */ 

/** @addtogroup Common
  * @{
  */

/** @addtogroup FONTS
  * @brief      This file provides text font20 for STM32xx-EVAL's LCD driver.
  * @{
  */  

/** @defgroup FONTS_Private_Types
  * @{
  */ 
/**
  * @}
  */ 


/** @defgroup FONTS_Private_Defines
  * @{
  */
/**
  * @}
  */ 


/** @defgroup FONTS_Private_Macros
  * @{
  */
/**
  * @}
  */ 
  

/** @defgroup FONTS_Private_Variables
  * @{
  */

// Character bitmaps for Courier New 15pt
const uint8_t Font20_Table[] = 
{
	// @0 ' ' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @40 '!' (14 pixels wide)
	0x00, 0x00, //               
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x02, 0x00, //       #       
	0x02, 0x00, //       #       
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @80 '"' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x1C, 0xE0, //    ###  ###   
	0x1C, 0xE0, //    ###  ###   
	0x1C, 0xE0, //    ###  ###   
	0x08, 0x40, //     #    #    
	0x08, 0x40, //     #    #    
	0x08, 0x40, //     #    #    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @120 '#' (14 pixels wide)
	0x0C, 0xC0, //     ##  ##    
	0x0C, 0xC0, //     ##  ##    
	0x0C, 0xC0, //     ##  ##    
	0x0C, 0xC0, //     ##  ##    
	0x0C, 0xC0, //     ##  ##    
	0x3F, 0xF0, //   ##########  
	0x3F, 0xF0, //   ##########  
	0x0C, 0xC0, //     ##  ##    
	0x0C, 0xC0, //     ##  ##    
	0x3F, 0xF0, //   ##########  
	0x3F, 0xF0, //   ##########  
	0x0C, 0xC0, //     ##  ##    
	0x0C, 0xC0, //     ##  ##    
	0x0C, 0xC0, //     ##  ##    
	0x0C, 0xC0, //     ##  ##    
	0x0C, 0xC0, //     ##  ##    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @160 '$' (14 pixels wide)
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x07, 0xE0, //      ######   
	0x0F, 0xE0, //     #######   
	0x18, 0x60, //    ##    ##   
	0x18, 0x00, //    ##         
	0x1F, 0x00, //    #####      
	0x0F, 0xC0, //     ######    
	0x00, 0xE0, //         ###   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x1F, 0xC0, //    #######    
	0x1F, 0x80, //    ######     
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @200 '%' (14 pixels wide)
	0x00, 0x00, //               
	0x1C, 0x00, //    ###        
	0x22, 0x00, //   #   #       
	0x22, 0x00, //   #   #       
	0x22, 0x00, //   #   #       
	0x1C, 0x60, //    ###   ##   
	0x01, 0xE0, //        ####   
	0x0F, 0x80, //     #####     
	0x3C, 0x00, //   ####        
	0x31, 0xC0, //   ##   ###    
	0x02, 0x20, //       #   #   
	0x02, 0x20, //       #   #   
	0x02, 0x20, //       #   #   
	0x01, 0xC0, //        ###    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @240 '&' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x03, 0xE0, //       #####   
	0x0F, 0xE0, //     #######   
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x06, 0x00, //      ##       
	0x0F, 0x30, //     ####  ##  
	0x1F, 0xF0, //    #########  
	0x19, 0xE0, //    ##  ####   
	0x18, 0xC0, //    ##   ##    
	0x1F, 0xF0, //    #########  
	0x07, 0xB0, //      #### ##  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @280 ''' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x03, 0x80, //       ###     
	0x03, 0x80, //       ###     
	0x03, 0x80, //       ###     
	0x01, 0x00, //        #      
	0x01, 0x00, //        #      
	0x01, 0x00, //        #      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @320 '(' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x01, 0x80, //        ##     
	0x01, 0x80, //        ##     
	0x01, 0x80, //        ##     
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x01, 0x80, //        ##     
	0x01, 0x80, //        ##     
	0x01, 0x80, //        ##     
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @360 ')' (14 pixels wide)
	0x00, 0x00, //               
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @400 '*' (14 pixels wide)
	0x00, 0x00, //               
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x1B, 0x60, //    ## ## ##   
	0x1F, 0xE0, //    ########   
	0x07, 0x80, //      ####     
	0x07, 0x80, //      ####     
	0x0F, 0xC0, //     ######    
	0x0C, 0xC0, //     ##  ##    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @440 '+' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x3F, 0xF0, //   ##########  
	0x3F, 0xF0, //   ##########  
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @480 ',' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x03, 0x80, //       ###     
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x04, 0x00, //      #        
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @520 '-' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3F, 0xE0, //   #########   
	0x3F, 0xE0, //   #########   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @560 '.' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x03, 0x80, //       ###     
	0x03, 0x80, //       ###     
	0x03, 0x80, //       ###     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @600 '/' (14 pixels wide)
	0x00, 0x60, //          ##   
	0x00, 0x60, //          ##   
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x01, 0x80, //        ##     
	0x01, 0x80, //        ##     
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x18, 0x00, //    ##         
	0x18, 0x00, //    ##         
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @640 '0' (14 pixels wide)
	0x00, 0x00, //               
	0x0F, 0x80, //     #####     
	0x1F, 0xC0, //    #######    
	0x18, 0xC0, //    ##   ##    
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x18, 0xC0, //    ##   ##    
	0x1F, 0xC0, //    #######    
	0x0F, 0x80, //     #####     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @680 '1' (14 pixels wide)
	0x00, 0x00, //               
	0x03, 0x00, //       ##      
	0x1F, 0x00, //    #####      
	0x1F, 0x00, //    #####      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @720 '2' (14 pixels wide)
	0x00, 0x00, //               
	0x0F, 0x80, //     #####     
	0x1F, 0xC0, //    #######    
	0x38, 0xE0, //   ###   ###   
	0x30, 0x60, //   ##     ##   
	0x00, 0x60, //          ##   
	0x00, 0xC0, //         ##    
	0x01, 0x80, //        ##     
	0x03, 0x00, //       ##      
	0x06, 0x00, //      ##       
	0x0C, 0x00, //     ##        
	0x18, 0x00, //    ##         
	0x3F, 0xE0, //   #########   
	0x3F, 0xE0, //   #########   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @760 '3' (14 pixels wide)
	0x00, 0x00, //               
	0x0F, 0x80, //     #####     
	0x3F, 0xC0, //   ########    
	0x30, 0xE0, //   ##    ###   
	0x00, 0x60, //          ##   
	0x00, 0xE0, //         ###   
	0x07, 0xC0, //      #####    
	0x07, 0xC0, //      #####    
	0x00, 0xE0, //         ###   
	0x00, 0x60, //          ##   
	0x00, 0x60, //          ##   
	0x60, 0xE0, //  ##     ###   
	0x7F, 0xC0, //  #########    
	0x3F, 0x80, //   #######     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @800 '4' (14 pixels wide)
	0x00, 0x00, //               
	0x01, 0xC0, //        ###    
	0x03, 0xC0, //       ####    
	0x03, 0xC0, //       ####    
	0x06, 0xC0, //      ## ##    
	0x0C, 0xC0, //     ##  ##    
	0x0C, 0xC0, //     ##  ##    
	0x18, 0xC0, //    ##   ##    
	0x30, 0xC0, //   ##    ##    
	0x3F, 0xE0, //   #########   
	0x3F, 0xE0, //   #########   
	0x00, 0xC0, //         ##    
	0x03, 0xE0, //       #####   
	0x03, 0xE0, //       #####   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @840 '5' (14 pixels wide)
	0x00, 0x00, //               
	0x1F, 0xC0, //    #######    
	0x1F, 0xC0, //    #######    
	0x18, 0x00, //    ##         
	0x18, 0x00, //    ##         
	0x1F, 0x80, //    ######     
	0x1F, 0xC0, //    #######    
	0x18, 0xE0, //    ##   ###   
	0x00, 0x60, //          ##   
	0x00, 0x60, //          ##   
	0x00, 0x60, //          ##   
	0x30, 0xE0, //   ##    ###   
	0x3F, 0xC0, //   ########    
	0x1F, 0x80, //    ######     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @880 '6' (14 pixels wide)
	0x00, 0x00, //               
	0x03, 0xE0, //       #####   
	0x0F, 0xE0, //     #######   
	0x1E, 0x00, //    ####       
	0x18, 0x00, //    ##         
	0x38, 0x00, //   ###         
	0x37, 0x80, //   ## ####     
	0x3F, 0xC0, //   ########    
	0x38, 0xE0, //   ###   ###   
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x18, 0xE0, //    ##   ###   
	0x1F, 0xC0, //    #######    
	0x07, 0x80, //      ####     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @920 '7' (14 pixels wide)
	0x00, 0x00, //               
	0x3F, 0xE0, //   #########   
	0x3F, 0xE0, //   #########   
	0x30, 0x60, //   ##     ##   
	0x00, 0x60, //          ##   
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x01, 0x80, //        ##     
	0x01, 0x80, //        ##     
	0x01, 0x80, //        ##     
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @960 '8' (14 pixels wide)
	0x00, 0x00, //               
	0x0F, 0x80, //     #####     
	0x1F, 0xC0, //    #######    
	0x38, 0xE0, //   ###   ###   
	0x30, 0x60, //   ##     ##   
	0x38, 0xE0, //   ###   ###   
	0x1F, 0xC0, //    #######    
	0x1F, 0xC0, //    #######    
	0x38, 0xE0, //   ###   ###   
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x38, 0xE0, //   ###   ###   
	0x1F, 0xC0, //    #######    
	0x0F, 0x80, //     #####     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1000 '9' (14 pixels wide)
	0x00, 0x00, //               
	0x0F, 0x00, //     ####      
	0x1F, 0xC0, //    #######    
	0x38, 0xC0, //   ###   ##    
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x38, 0xE0, //   ###   ###   
	0x1F, 0xE0, //    ########   
	0x0F, 0x60, //     #### ##   
	0x00, 0xE0, //         ###   
	0x00, 0xC0, //         ##    
	0x03, 0xC0, //       ####    
	0x3F, 0x80, //   #######     
	0x3E, 0x00, //   #####       
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1040 ':' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x03, 0x80, //       ###     
	0x03, 0x80, //       ###     
	0x03, 0x80, //       ###     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x03, 0x80, //       ###     
	0x03, 0x80, //       ###     
	0x03, 0x80, //       ###     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1080 ';' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x01, 0xC0, //        ###    
	0x01, 0xC0, //        ###    
	0x01, 0xC0, //        ###    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x03, 0x80, //       ###     
	0x03, 0x00, //       ##      
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x04, 0x00, //      #        
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1120 '<' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x30, //           ##  
	0x00, 0xF0, //         ####  
	0x03, 0xC0, //       ####    
	0x07, 0x00, //      ###      
	0x1C, 0x00, //    ###        
	0x78, 0x00, //  ####         
	0x1C, 0x00, //    ###        
	0x07, 0x00, //      ###      
	0x03, 0xC0, //       ####    
	0x00, 0xF0, //         ####  
	0x00, 0x30, //           ##  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1160 '=' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x7F, 0xF0, //  ###########  
	0x7F, 0xF0, //  ###########  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x7F, 0xF0, //  ###########  
	0x7F, 0xF0, //  ###########  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1200 '>' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x30, 0x00, //   ##          
	0x3C, 0x00, //   ####        
	0x0F, 0x00, //     ####      
	0x03, 0x80, //       ###     
	0x00, 0xE0, //         ###   
	0x00, 0x78, //          #### 
	0x00, 0xE0, //         ###   
	0x03, 0x80, //       ###     
	0x0F, 0x00, //     ####      
	0x3C, 0x00, //   ####        
	0x30, 0x00, //   ##          
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1240 '?' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x0F, 0x80, //     #####     
	0x1F, 0xC0, //    #######    
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x00, 0x60, //          ##   
	0x01, 0xC0, //        ###    
	0x03, 0x80, //       ###     
	0x03, 0x00, //       ##      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1280 '@' (14 pixels wide)
	0x00, 0x00, //               
	0x03, 0x80, //       ###     
	0x0C, 0x80, //     ##  #     
	0x08, 0x40, //     #    #    
	0x10, 0x40, //    #     #    
	0x10, 0x40, //    #     #    
	0x11, 0xC0, //    #   ###    
	0x12, 0x40, //    #  #  #    
	0x12, 0x40, //    #  #  #    
	0x12, 0x40, //    #  #  #    
	0x11, 0xC0, //    #   ###    
	0x10, 0x00, //    #          
	0x08, 0x00, //     #         
	0x08, 0x40, //     #    #    
	0x07, 0x80, //      ####     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1320 'A' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x1F, 0x80, //    ######     
	0x1F, 0x80, //    ######     
	0x03, 0x80, //       ###     
	0x06, 0xC0, //      ## ##    
	0x06, 0xC0, //      ## ##    
	0x0C, 0xC0, //     ##  ##    
	0x0C, 0x60, //     ##   ##   
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x30, 0x30, //   ##      ##  
	0x78, 0x78, //  ####    #### 
	0x78, 0x78, //  ####    #### 
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1360 'B' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3F, 0x80, //   #######     
	0x3F, 0xC0, //   ########    
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0xE0, //    ##   ###   
	0x1F, 0xC0, //    #######    
	0x1F, 0xE0, //    ########   
	0x18, 0x70, //    ##    ###  
	0x18, 0x30, //    ##     ##  
	0x18, 0x30, //    ##     ##  
	0x3F, 0xF0, //   ##########  
	0x3F, 0xE0, //   #########   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1400 'C' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0xB0, //      #### ##  
	0x0F, 0xF0, //     ########  
	0x1C, 0x70, //    ###   ###  
	0x38, 0x30, //   ###     ##  
	0x30, 0x00, //   ##          
	0x30, 0x00, //   ##          
	0x30, 0x00, //   ##          
	0x30, 0x00, //   ##          
	0x38, 0x30, //   ###     ##  
	0x1C, 0x70, //    ###   ###  
	0x0F, 0xE0, //     #######   
	0x07, 0xC0, //      #####    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1440 'D' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x7F, 0x80, //  ########     
	0x7F, 0xC0, //  #########    
	0x30, 0xE0, //   ##    ###   
	0x30, 0x70, //   ##     ###  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x70, //   ##     ###  
	0x30, 0xE0, //   ##    ###   
	0x7F, 0xC0, //  #########    
	0x7F, 0x80, //  ########     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1480 'E' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3F, 0xF0, //   ##########  
	0x3F, 0xF0, //   ##########  
	0x18, 0x30, //    ##     ##  
	0x18, 0x30, //    ##     ##  
	0x19, 0x80, //    ##  ##     
	0x1F, 0x80, //    ######     
	0x1F, 0x80, //    ######     
	0x19, 0x80, //    ##  ##     
	0x18, 0x30, //    ##     ##  
	0x18, 0x30, //    ##     ##  
	0x3F, 0xF0, //   ##########  
	0x3F, 0xF0, //   ##########  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1520 'F' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3F, 0xF0, //   ##########  
	0x3F, 0xF0, //   ##########  
	0x18, 0x30, //    ##     ##  
	0x18, 0x30, //    ##     ##  
	0x19, 0x80, //    ##  ##     
	0x1F, 0x80, //    ######     
	0x1F, 0x80, //    ######     
	0x19, 0x80, //    ##  ##     
	0x18, 0x00, //    ##         
	0x18, 0x00, //    ##         
	0x3F, 0x00, //   ######      
	0x3F, 0x00, //   ######      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1560 'G' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0xB0, //      #### ##  
	0x1F, 0xF0, //    #########  
	0x18, 0x70, //    ##    ###  
	0x30, 0x30, //   ##      ##  
	0x30, 0x00, //   ##          
	0x30, 0x00, //   ##          
	0x31, 0xF8, //   ##   ###### 
	0x31, 0xF8, //   ##   ###### 
	0x30, 0x30, //   ##      ##  
	0x18, 0x30, //    ##     ##  
	0x1F, 0xF0, //    #########  
	0x07, 0xC0, //      #####    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1600 'H' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3C, 0xF0, //   ####  ####  
	0x3C, 0xF0, //   ####  ####  
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x3C, 0xF0, //   ####  ####  
	0x3C, 0xF0, //   ####  ####  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1640 'I' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1680 'J' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x03, 0xF8, //       ####### 
	0x03, 0xF8, //       ####### 
	0x00, 0x60, //          ##   
	0x00, 0x60, //          ##   
	0x00, 0x60, //          ##   
	0x00, 0x60, //          ##   
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x30, 0xE0, //   ##    ###   
	0x3F, 0xC0, //   ########    
	0x0F, 0x80, //     #####     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1720 'K' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3E, 0xF8, //   ##### ##### 
	0x3E, 0xF8, //   ##### ##### 
	0x18, 0xE0, //    ##   ###   
	0x19, 0x80, //    ##  ##     
	0x1B, 0x00, //    ## ##      
	0x1F, 0x00, //    #####      
	0x1D, 0x80, //    ### ##     
	0x18, 0xC0, //    ##   ##    
	0x18, 0xC0, //    ##   ##    
	0x18, 0x60, //    ##    ##   
	0x3E, 0x78, //   #####  #### 
	0x3E, 0x38, //   #####   ### 
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1760 'L' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3F, 0x00, //   ######      
	0x3F, 0x00, //   ######      
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x0C, 0x30, //     ##    ##  
	0x0C, 0x30, //     ##    ##  
	0x0C, 0x30, //     ##    ##  
	0x3F, 0xF0, //   ##########  
	0x3F, 0xF0, //   ##########  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1800 'M' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x78, 0x78, //  ####    #### 
	0x78, 0x78, //  ####    #### 
	0x38, 0x70, //   ###    ###  
	0x3C, 0xF0, //   ####  ####  
	0x34, 0xB0, //   ## #  # ##  
	0x37, 0xB0, //   ## #### ##  
	0x37, 0xB0, //   ## #### ##  
	0x33, 0x30, //   ##  ##  ##  
	0x33, 0x30, //   ##  ##  ##  
	0x30, 0x30, //   ##      ##  
	0x7C, 0xF8, //  #####  ##### 
	0x7C, 0xF8, //  #####  ##### 
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1840 'N' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x39, 0xF0, //   ###  #####  
	0x3D, 0xF0, //   #### #####  
	0x1C, 0x60, //    ###   ##   
	0x1E, 0x60, //    ####  ##   
	0x1E, 0x60, //    ####  ##   
	0x1B, 0x60, //    ## ## ##   
	0x1B, 0x60, //    ## ## ##   
	0x19, 0xE0, //    ##  ####   
	0x19, 0xE0, //    ##  ####   
	0x18, 0xE0, //    ##   ###   
	0x3E, 0xE0, //   ##### ###   
	0x3E, 0x60, //   #####  ##   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1880 'O' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0x80, //      ####     
	0x0F, 0xC0, //     ######    
	0x1C, 0xE0, //    ###  ###   
	0x38, 0x70, //   ###    ###  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x38, 0x70, //   ###    ###  
	0x1C, 0xE0, //    ###  ###   
	0x0F, 0xC0, //     ######    
	0x07, 0x80, //      ####     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1920 'P' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3F, 0xC0, //   ########    
	0x3F, 0xE0, //   #########   
	0x18, 0x70, //    ##    ###  
	0x18, 0x30, //    ##     ##  
	0x18, 0x30, //    ##     ##  
	0x18, 0x70, //    ##    ###  
	0x1F, 0xE0, //    ########   
	0x1F, 0xC0, //    #######    
	0x18, 0x00, //    ##         
	0x18, 0x00, //    ##         
	0x3F, 0x00, //   ######      
	0x3F, 0x00, //   ######      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @1960 'Q' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0x80, //      ####     
	0x0F, 0xC0, //     ######    
	0x1C, 0xE0, //    ###  ###   
	0x38, 0x70, //   ###    ###  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x38, 0x70, //   ###    ###  
	0x1C, 0xE0, //    ###  ###   
	0x0F, 0xC0, //     ######    
	0x07, 0x80, //      ####     
	0x07, 0xB0, //      #### ##  
	0x0F, 0xF0, //     ########  
	0x0C, 0xE0, //     ##  ###   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2000 'R' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3F, 0xC0, //   ########    
	0x3F, 0xE0, //   #########   
	0x18, 0x70, //    ##    ###  
	0x18, 0x30, //    ##     ##  
	0x18, 0x70, //    ##    ###  
	0x1F, 0xE0, //    ########   
	0x1F, 0xC0, //    #######    
	0x18, 0xE0, //    ##   ###   
	0x18, 0x60, //    ##    ##   
	0x18, 0x70, //    ##    ###  
	0x3E, 0x38, //   #####   ### 
	0x3E, 0x18, //   #####    ## 
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2040 'S' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x0F, 0xB0, //     ##### ##  
	0x1F, 0xF0, //    #########  
	0x38, 0x70, //   ###    ###  
	0x30, 0x30, //   ##      ##  
	0x38, 0x00, //   ###         
	0x1F, 0x80, //    ######     
	0x07, 0xE0, //      ######   
	0x00, 0x70, //          ###  
	0x30, 0x30, //   ##      ##  
	0x38, 0x70, //   ###    ###  
	0x3F, 0xE0, //   #########   
	0x37, 0xC0, //   ## #####    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2080 'T' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3F, 0xF0, //   ##########  
	0x3F, 0xF0, //   ##########  
	0x33, 0x30, //   ##  ##  ##  
	0x33, 0x30, //   ##  ##  ##  
	0x33, 0x30, //   ##  ##  ##  
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x0F, 0xC0, //     ######    
	0x0F, 0xC0, //     ######    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2120 'U' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3C, 0xF0, //   ####  ####  
	0x3C, 0xF0, //   ####  ####  
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x1C, 0xE0, //    ###  ###   
	0x0F, 0xC0, //     ######    
	0x07, 0x80, //      ####     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2160 'V' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x78, 0xF0, //  ####   ####  
	0x78, 0xF0, //  ####   ####  
	0x30, 0x60, //   ##     ##   
	0x30, 0x60, //   ##     ##   
	0x18, 0xC0, //    ##   ##    
	0x18, 0xC0, //    ##   ##    
	0x0D, 0x80, //     ## ##     
	0x0D, 0x80, //     ## ##     
	0x0D, 0x80, //     ## ##     
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2200 'W' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x7C, 0x7C, //  #####   #####
	0x7C, 0x7C, //  #####   #####
	0x30, 0x18, //   ##       ## 
	0x33, 0x98, //   ##  ###  ## 
	0x33, 0x98, //   ##  ###  ## 
	0x33, 0x98, //   ##  ###  ## 
	0x36, 0xD8, //   ## ## ## ## 
	0x16, 0xD0, //    # ## ## #  
	0x1C, 0x70, //    ###   ###  
	0x1C, 0x70, //    ###   ###  
	0x1C, 0x70, //    ###   ###  
	0x18, 0x30, //    ##     ##  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2240 'X' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x78, 0xF0, //  ####   ####  
	0x78, 0xF0, //  ####   ####  
	0x30, 0x60, //   ##     ##   
	0x18, 0xC0, //    ##   ##    
	0x0D, 0x80, //     ## ##     
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x0D, 0x80, //     ## ##     
	0x18, 0xC0, //    ##   ##    
	0x30, 0x60, //   ##     ##   
	0x78, 0xF0, //  ####   ####  
	0x78, 0xF0, //  ####   ####  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2280 'Y' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3C, 0xF0, //   ####  ####  
	0x3C, 0xF0, //   ####  ####  
	0x18, 0x60, //    ##    ##   
	0x0C, 0xC0, //     ##  ##    
	0x07, 0x80, //      ####     
	0x07, 0x80, //      ####     
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x0F, 0xC0, //     ######    
	0x0F, 0xC0, //     ######    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2320 'Z' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x18, 0x60, //    ##    ##   
	0x18, 0xC0, //    ##   ##    
	0x01, 0x80, //        ##     
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x06, 0x00, //      ##       
	0x0C, 0x60, //     ##   ##   
	0x18, 0x60, //    ##    ##   
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2360 '[' (14 pixels wide)
	0x00, 0x00, //               
	0x03, 0xC0, //       ####    
	0x03, 0xC0, //       ####    
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0xC0, //       ####    
	0x03, 0xC0, //       ####    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2400 '\' (14 pixels wide)
	0x18, 0x00, //    ##         
	0x18, 0x00, //    ##         
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x01, 0x80, //        ##     
	0x01, 0x80, //        ##     
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x00, 0x60, //          ##   
	0x00, 0x60, //          ##   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2440 ']' (14 pixels wide)
	0x00, 0x00, //               
	0x0F, 0x00, //     ####      
	0x0F, 0x00, //     ####      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x0F, 0x00, //     ####      
	0x0F, 0x00, //     ####      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2480 '^' (14 pixels wide)
	0x00, 0x00, //               
	0x02, 0x00, //       #       
	0x07, 0x00, //      ###      
	0x0D, 0x80, //     ## ##     
	0x18, 0xC0, //    ##   ##    
	0x30, 0x60, //   ##     ##   
	0x20, 0x20, //   #       #   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2520 '_' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0xFF, 0xFC, // ##############
	0xFF, 0xFC, // ##############

	// @2560 '`' (14 pixels wide)
	0x00, 0x00, //               
	0x04, 0x00, //      #        
	0x03, 0x00, //       ##      
	0x00, 0x80, //         #     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2600 'a' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x0F, 0xC0, //     ######    
	0x1F, 0xE0, //    ########   
	0x00, 0x60, //          ##   
	0x0F, 0xE0, //     #######   
	0x1F, 0xE0, //    ########   
	0x38, 0x60, //   ###    ##   
	0x30, 0xE0, //   ##    ###   
	0x3F, 0xF0, //   ##########  
	0x1F, 0x70, //    ##### ###  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2640 'b' (14 pixels wide)
	0x00, 0x00, //               
	0x70, 0x00, //  ###          
	0x70, 0x00, //  ###          
	0x30, 0x00, //   ##          
	0x30, 0x00, //   ##          
	0x37, 0x80, //   ## ####     
	0x3F, 0xE0, //   #########   
	0x38, 0x60, //   ###    ##   
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x38, 0x60, //   ###    ##   
	0x7F, 0xE0, //  ##########   
	0x77, 0x80, //  ### ####     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2680 'c' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0xB0, //      #### ##  
	0x1F, 0xF0, //    #########  
	0x18, 0x30, //    ##     ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x00, //   ##          
	0x30, 0x00, //   ##          
	0x38, 0x30, //   ###     ##  
	0x1F, 0xF0, //    #########  
	0x0F, 0xC0, //     ######    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2720 'd' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x70, //          ###  
	0x00, 0x70, //          ###  
	0x00, 0x30, //           ##  
	0x00, 0x30, //           ##  
	0x07, 0xB0, //      #### ##  
	0x1F, 0xF0, //    #########  
	0x18, 0x70, //    ##    ###  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x38, 0x70, //   ###    ###  
	0x1F, 0xF8, //    ########## 
	0x07, 0xB8, //      #### ### 
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2760 'e' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0x80, //      ####     
	0x1F, 0xE0, //    ########   
	0x18, 0x60, //    ##    ##   
	0x3F, 0xF0, //   ##########  
	0x3F, 0xF0, //   ##########  
	0x30, 0x00, //   ##          
	0x18, 0x30, //    ##     ##  
	0x1F, 0xF0, //    #########  
	0x07, 0xC0, //      #####    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2800 'f' (14 pixels wide)
	0x00, 0x00, //               
	0x03, 0xF0, //       ######  
	0x07, 0xF0, //      #######  
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2840 'g' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0xB8, //      #### ### 
	0x1F, 0xF8, //    ########## 
	0x18, 0x70, //    ##    ###  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x18, 0x70, //    ##    ###  
	0x1F, 0xF0, //    #########  
	0x07, 0xB0, //      #### ##  
	0x00, 0x30, //           ##  
	0x00, 0x70, //          ###  
	0x0F, 0xE0, //     #######   
	0x0F, 0xC0, //     ######    
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2880 'h' (14 pixels wide)
	0x00, 0x00, //               
	0x38, 0x00, //   ###         
	0x38, 0x00, //   ###         
	0x18, 0x00, //    ##         
	0x18, 0x00, //    ##         
	0x1B, 0xC0, //    ## ####    
	0x1F, 0xE0, //    ########   
	0x1C, 0x60, //    ###   ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x3C, 0xF0, //   ####  ####  
	0x3C, 0xF0, //   ####  ####  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2920 'i' (14 pixels wide)
	0x00, 0x00, //               
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x1F, 0x00, //    #####      
	0x1F, 0x00, //    #####      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @2960 'j' (14 pixels wide)
	0x00, 0x00, //               
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x1F, 0xC0, //    #######    
	0x1F, 0xC0, //    #######    
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x00, 0xC0, //         ##    
	0x01, 0xC0, //        ###    
	0x3F, 0x80, //   #######     
	0x3F, 0x00, //   ######      
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3000 'k' (14 pixels wide)
	0x00, 0x00, //               
	0x38, 0x00, //   ###         
	0x38, 0x00, //   ###         
	0x18, 0x00, //    ##         
	0x18, 0x00, //    ##         
	0x1B, 0xE0, //    ## #####   
	0x1B, 0xE0, //    ## #####   
	0x1B, 0x00, //    ## ##      
	0x1E, 0x00, //    ####       
	0x1E, 0x00, //    ####       
	0x1B, 0x00, //    ## ##      
	0x19, 0x80, //    ##  ##     
	0x39, 0xF0, //   ###  #####  
	0x39, 0xF0, //   ###  #####  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3040 'l' (14 pixels wide)
	0x00, 0x00, //               
	0x1F, 0x00, //    #####      
	0x1F, 0x00, //    #####      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3080 'm' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x7E, 0xE0, //  ###### ###   
	0x7F, 0xF0, //  ###########  
	0x33, 0x30, //   ##  ##  ##  
	0x33, 0x30, //   ##  ##  ##  
	0x33, 0x30, //   ##  ##  ##  
	0x33, 0x30, //   ##  ##  ##  
	0x33, 0x30, //   ##  ##  ##  
	0x7B, 0xB8, //  #### ### ### 
	0x7B, 0xB8, //  #### ### ### 
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3120 'n' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3B, 0xC0, //   ### ####    
	0x3F, 0xE0, //   #########   
	0x1C, 0x60, //    ###   ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x3C, 0xF0, //   ####  ####  
	0x3C, 0xF0, //   ####  ####  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3160 'o' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0x80, //      ####     
	0x1F, 0xE0, //    ########   
	0x18, 0x60, //    ##    ##   
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x18, 0x60, //    ##    ##   
	0x1F, 0xE0, //    ########   
	0x07, 0x80, //      ####     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3200 'p' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x77, 0x80, //  ### ####     
	0x7F, 0xE0, //  ##########   
	0x38, 0x60, //   ###    ##   
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x38, 0x60, //   ###    ##   
	0x3F, 0xE0, //   #########   
	0x37, 0x80, //   ## ####     
	0x30, 0x00, //   ##          
	0x30, 0x00, //   ##          
	0x7C, 0x00, //  #####        
	0x7C, 0x00, //  #####        
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3240 'q' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0xB8, //      #### ### 
	0x1F, 0xF8, //    ########## 
	0x18, 0x70, //    ##    ###  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x30, 0x30, //   ##      ##  
	0x18, 0x70, //    ##    ###  
	0x1F, 0xF0, //    #########  
	0x07, 0xB0, //      #### ##  
	0x00, 0x30, //           ##  
	0x00, 0x30, //           ##  
	0x00, 0xF8, //         ##### 
	0x00, 0xF8, //         ##### 
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3280 'r' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3C, 0xE0, //   ####  ###   
	0x3D, 0xF0, //   #### #####  
	0x0F, 0x30, //     ####  ##  
	0x0E, 0x00, //     ###       
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x3F, 0xC0, //   ########    
	0x3F, 0xC0, //   ########    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3320 's' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x07, 0xE0, //      ######   
	0x1F, 0xE0, //    ########   
	0x18, 0x60, //    ##    ##   
	0x1E, 0x00, //    ####       
	0x0F, 0xC0, //     ######    
	0x01, 0xE0, //        ####   
	0x18, 0x60, //    ##    ##   
	0x1F, 0xE0, //    ########   
	0x1F, 0x80, //    ######     
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3360 't' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x3F, 0xE0, //   #########   
	0x3F, 0xE0, //   #########   
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x0C, 0x00, //     ##        
	0x0C, 0x30, //     ##    ##  
	0x0F, 0xF0, //     ########  
	0x07, 0xC0, //      #####    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3400 'u' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x38, 0xE0, //   ###   ###   
	0x38, 0xE0, //   ###   ###   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0x60, //    ##    ##   
	0x18, 0xE0, //    ##   ###   
	0x1F, 0xF0, //    #########  
	0x0F, 0x70, //     #### ###  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3440 'v' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x78, 0xF0, //  ####   ####  
	0x78, 0xF0, //  ####   ####  
	0x30, 0x60, //   ##     ##   
	0x18, 0xC0, //    ##   ##    
	0x18, 0xC0, //    ##   ##    
	0x0D, 0x80, //     ## ##     
	0x0D, 0x80, //     ## ##     
	0x07, 0x00, //      ###      
	0x07, 0x00, //      ###      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3480 'w' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x78, 0xF0, //  ####   ####  
	0x78, 0xF0, //  ####   ####  
	0x32, 0x60, //   ##  #  ##   
	0x32, 0x60, //   ##  #  ##   
	0x37, 0xE0, //   ## ######   
	0x1D, 0xC0, //    ### ###    
	0x1D, 0xC0, //    ### ###    
	0x18, 0xC0, //    ##   ##    
	0x18, 0xC0, //    ##   ##    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3520 'x' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x3C, 0xF0, //   ####  ####  
	0x3C, 0xF0, //   ####  ####  
	0x0C, 0xC0, //     ##  ##    
	0x07, 0x80, //      ####     
	0x03, 0x00, //       ##      
	0x07, 0x80, //      ####     
	0x0C, 0xC0, //     ##  ##    
	0x3C, 0xF0, //   ####  ####  
	0x3C, 0xF0, //   ####  ####  
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3560 'y' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x78, 0xF0, //  ####   ####  
	0x78, 0xF0, //  ####   ####  
	0x30, 0x60, //   ##     ##   
	0x18, 0xC0, //    ##   ##    
	0x18, 0xC0, //    ##   ##    
	0x0D, 0x80, //     ## ##     
	0x0F, 0x80, //     #####     
	0x07, 0x00, //      ###      
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x0C, 0x00, //     ##        
	0x7F, 0x00, //  #######      
	0x7F, 0x00, //  #######      
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3600 'z' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x18, 0xC0, //    ##   ##    
	0x01, 0x80, //        ##     
	0x03, 0x00, //       ##      
	0x06, 0x00, //      ##       
	0x0C, 0x60, //     ##   ##   
	0x1F, 0xE0, //    ########   
	0x1F, 0xE0, //    ########   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3640 '{' (14 pixels wide)
	0x00, 0x00, //               
	0x01, 0xC0, //        ###    
	0x03, 0xC0, //       ####    
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x07, 0x00, //      ###      
	0x0E, 0x00, //     ###       
	0x07, 0x00, //      ###      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0xC0, //       ####    
	0x01, 0xC0, //        ###    
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3680 '|' (14 pixels wide)
	0x00, 0x00, //               
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x03, 0x00, //       ##      
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3720 '}' (14 pixels wide)
	0x00, 0x00, //               
	0x1C, 0x00, //    ###        
	0x1E, 0x00, //    ####       
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x07, 0x00, //      ###      
	0x03, 0x80, //       ###     
	0x07, 0x00, //      ###      
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x06, 0x00, //      ##       
	0x1E, 0x00, //    ####       
	0x1C, 0x00, //    ###        
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               

	// @3760 '~' (14 pixels wide)
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x0E, 0x00, //     ###       
	0x3F, 0x30, //   ######  ##  
	0x33, 0xF0, //   ##  ######  
	0x01, 0xE0, //        ####   
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
	0x00, 0x00, //               
};


sFONT Font20 = {
  Font20_Table,
  14, /* Width */
  20, /* Height */
  2,  /* Size = bytes per pixel row */
};

/**
  * @}
  */ 


/** @defgroup FONTS_Private_Function_Prototypes
  * @{
  */ 
/**
  * @}
  */ 


/** @defgroup FONTS_Private_Functions
  * @{
  */
    
/**
  * @}
  */
  
/**
  * @}
  */ 

/**
  * @}
  */

/**
  * @}
  */

/**
  * @}
  */  
/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
