# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/CMakeLists.txt"
  "CMakeFiles/3.28.1/CMakeASMCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeSystem.cmake"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/gcc-arm-none-eabi.cmake"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/CMakeLists.txt"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeASMCompiler.cmake.in"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeASMInformation.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCCompiler.cmake.in"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCCompilerABI.c"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCInformation.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompiler.cmake.in"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCompilerIdDetection.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineASMCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompileFeatures.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeFindBinUtils.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeParseLibraryArchitecture.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeSystem.cmake.in"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeTestASMCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeTestCompilerCommon.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeUnixFindMake.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-ASM.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-C.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/GNU.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/LCC-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Internal/FeatureTesting.cmake"
  "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/Platform/Generic.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.28.1/CMakeSystem.cmake"
  "CMakeFiles/3.28.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeCCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.28.1/CMakeASMCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "cmake/stm32cubemx/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/SPI_Screen001.dir/DependInfo.cmake"
  "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/DependInfo.cmake"
  )
