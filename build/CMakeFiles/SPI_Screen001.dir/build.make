# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake

# The command to remove a file.
RM = /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build

# Include any dependencies generated for this target.
include CMakeFiles/SPI_Screen001.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/SPI_Screen001.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/SPI_Screen001.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/SPI_Screen001.dir/flags.make

CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/main.c
CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/main.c

CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/main.c > CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/main.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.s

CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_it.c
CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_it.c

CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_it.c > CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_it.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.s

CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_hal_msp.c
CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_hal_msp.c

CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_hal_msp.c > CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_hal_msp.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.s

CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/sysmem.c
CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/sysmem.c

CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/sysmem.c > CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/sysmem.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.s

CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/syscalls.c
CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/syscalls.c

CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/syscalls.c > CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/syscalls.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.s

CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/startup_stm32f103xb.s
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building ASM object CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -o CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/startup_stm32f103xb.s

CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing ASM source to CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/startup_stm32f103xb.s > CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.i

CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling ASM source to assembly CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/startup_stm32f103xb.s -o CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.s

CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735.c
CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735.c

CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735.c > CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.s

CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735_test.c
CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735_test.c

CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735_test.c > CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735_test.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.s

CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font8.c
CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font8.c

CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font8.c > CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font8.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.s

CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font12.c
CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font12.c

CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font12.c > CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font12.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.s

CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font16.c
CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font16.c

CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font16.c > CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font16.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.s

CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font20.c
CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font20.c

CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font20.c > CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font20.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.s

CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj: CMakeFiles/SPI_Screen001.dir/flags.make
CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj: /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font24.c
CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj: CMakeFiles/SPI_Screen001.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj -MF CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj.d -o CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font24.c

CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.i"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font24.c > CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.i

CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.s"
	/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font24.c -o CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.s

# Object files for target SPI_Screen001
SPI_Screen001_OBJECTS = \
"CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj" \
"CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj" \
"CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj" \
"CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj" \
"CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj" \
"CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.obj" \
"CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj" \
"CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj" \
"CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj" \
"CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj" \
"CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj" \
"CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj" \
"CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj"

# External object files for target SPI_Screen001
SPI_Screen001_EXTERNAL_OBJECTS = \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32f1xx.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c.obj" \
"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c.obj"

SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32f1xx.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c.obj
SPI_Screen001.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c.obj
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/build.make
SPI_Screen001.elf: CMakeFiles/SPI_Screen001.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Linking C executable SPI_Screen001.elf"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/SPI_Screen001.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/SPI_Screen001.dir/build: SPI_Screen001.elf
.PHONY : CMakeFiles/SPI_Screen001.dir/build

CMakeFiles/SPI_Screen001.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/SPI_Screen001.dir/cmake_clean.cmake
.PHONY : CMakeFiles/SPI_Screen001.dir/clean

CMakeFiles/SPI_Screen001.dir/depend:
	cd /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001 /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001 /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/SPI_Screen001.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/SPI_Screen001.dir/depend

