
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "ASM"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_ASM
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/startup_stm32f103xb.s" "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.obj"
  )
set(CMAKE_ASM_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_ASM
  "DEBUG"
  "STM32F103xB"
  "USE_HAL_DRIVER"
  )

# The include file search paths:
set(CMAKE_ASM_TARGET_INCLUDE_PATH
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include"
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font12.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj.d"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font16.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj.d"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font20.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj.d"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font24.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj.d"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font8.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj.d"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/main.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj.d"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_hal_msp.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj.d"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_it.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj.d"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/syscalls.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj.d"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/sysmem.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj.d"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj.d"
  "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735_test.c" "CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj" "gcc" "CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
