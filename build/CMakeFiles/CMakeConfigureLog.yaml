
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake:228 (message)"
      - "CMakeLists.txt:28 (project)"
    message: |
      The target system is: Generic -  - arm
      The host system is: Darwin - 23.2.0 - arm64
  -
    kind: "message-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc 
      Build flags: -mcpu=cortex-m3;-Wall;-fdata-sections;-ffunction-sections;-mcpu=cortex-m3;-Wall;-fdata-sections;-ffunction-sections
      Id flags:  
      
      The output was:
      1
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-exit.o): in function `exit':
      (.text.exit+0x14): undefined reference to `_exit'
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-closer.o): in function `_close_r':
      (.text._close_r+0xc): undefined reference to `_close'
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-lseekr.o): in function `_lseek_r':
      (.text._lseek_r+0x10): undefined reference to `_lseek'
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-readr.o): in function `_read_r':
      (.text._read_r+0x10): undefined reference to `_read'
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-sbrkr.o): in function `_sbrk_r':
      (.text._sbrk_r+0xc): undefined reference to `_sbrk'
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-writer.o): in function `_write_r':
      (.text._write_r+0x10): undefined reference to `_write'
      collect2: error: ld returned 1 exit status
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc 
      Build flags: -mcpu=cortex-m3;-Wall;-fdata-sections;-ffunction-sections;-mcpu=cortex-m3;-Wall;-fdata-sections;-ffunction-sections
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"
      
      The C compiler identification is GNU, found in:
        /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/3.28.1/CompilerIdC/CMakeCCompilerId.o
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-g++ 
      Build flags: -mcpu=cortex-m3;-Wall;-fdata-sections;-ffunction-sections;-mcpu=cortex-m3;-Wall;-fdata-sections;-ffunction-sections;-fno-rtti;-fno-exceptions;-fno-threadsafe-statics
      Id flags:  
      
      The output was:
      1
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-exit.o): in function `exit':
      (.text.exit+0x14): undefined reference to `_exit'
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-closer.o): in function `_close_r':
      (.text._close_r+0xc): undefined reference to `_close'
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-lseekr.o): in function `_lseek_r':
      (.text._lseek_r+0x10): undefined reference to `_lseek'
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-readr.o): in function `_read_r':
      (.text._read_r+0x10): undefined reference to `_read'
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-sbrkr.o): in function `_sbrk_r':
      (.text._sbrk_r+0xc): undefined reference to `_sbrk'
      /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/ld: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/libc.a(libc_a-writer.o): in function `_write_r':
      (.text._write_r+0x10): undefined reference to `_write'
      collect2: error: ld returned 1 exit status
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-g++ 
      Build flags: -mcpu=cortex-m3;-Wall;-fdata-sections;-ffunction-sections;-mcpu=cortex-m3;-Wall;-fdata-sections;-ffunction-sections;-fno-rtti;-fno-exceptions;-fno-threadsafe-statics
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"
      
      The CXX compiler identification is GNU, found in:
        /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/3.28.1/CompilerIdCXX/CMakeCXXCompilerId.o
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:28 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/CMakeScratch/TryCompile-qSOeDo"
      binary: "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/CMakeScratch/TryCompile-qSOeDo"
    cmakeVariables:
      CMAKE_C_FLAGS: " -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections"
      CMAKE_C_FLAGS_DEBUG: "-O0 -g3"
      CMAKE_EXE_LINKER_FLAGS: "-mcpu=cortex-m3  -T \"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/STM32F103XX_FLASH.ld\" --specs=nano.specs -Wl,-Map=SPI_Screen001.map -Wl,--gc-sections -Wl,--print-memory-usage"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/CMakeScratch/TryCompile-qSOeDo'
        
        Run Build Command(s): /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_0f3f6/fast
        /Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_0f3f6.dir/build.make CMakeFiles/cmTC_0f3f6.dir/build
        Building C object CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj
        /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc   -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections  -std=gnu11   -v -o CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj -c /opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc
        Target: arm-none-eabi
        Configured with: /build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/src/gcc/configure --target=arm-none-eabi --prefix=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native --libexecdir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/lib --infodir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/info --mandir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/man --htmldir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/html --pdfdir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/pdf --enable-checking=release --enable-languages=c,c++ --enable-plugins --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-newlib --with-headers=yes --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/arm-none-eabi --with-zstd=no --build=x86_64-apple-darwin10 --host=x86_64-apple-darwin10 --with-gmp=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-mpfr=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-mpc=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-isl=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-lstdc++ -lm' --with-pkgversion='GNU Tools for STM32 13.3.rel1.20240926-1715' --with-multilib-list=rmprofile,aprofile
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 13.3.1 20240614 (GNU Tools for STM32 13.3.rel1.20240926-1715) 
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m3' '-Wall' '-fdata-sections' '-ffunction-sections' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv7-m' '-march=armv7-m' '-dumpdir' 'CMakeFiles/cmTC_0f3f6.dir/'
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/cc1 -quiet -v -imultilib thumb/v7-m/nofp -iprefix /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/ -isysroot /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi -D__USES_INITFINI__ /opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_0f3f6.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mcpu=cortex-m3 -mfloat-abi=soft -mthumb -mlibarch=armv7-m -march=armv7-m -Wall -std=gnu11 -version -fdata-sections -ffunction-sections -o /var/folders/lm/tmxn7r012t1dv_9flxb_mv_40000gn/T//ccRdZ9rB.s
        GNU C11 (GNU Tools for STM32 13.3.rel1.20240926-1715) version 13.3.1 20240614 (arm-none-eabi)
        	compiled by GNU C version 4.2.1 Compatible Apple LLVM 10.0.1 (clang-1001.0.46.4), GMP version 6.2.1, MPFR version 3.1.6, MPC version 1.0.3, isl version isl-0.15-1-g835ea3a-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/include"
        ignoring nonexistent directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/usr/local/include"
        ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/include-fixed"
        ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include"
        ignoring nonexistent directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/usr/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include-fixed
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include
        End of search list.
        Compiler executable checksum: 9f5afc2b4ae1532267b7b37af76b05d0
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m3' '-Wall' '-fdata-sections' '-ffunction-sections' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv7-m' '-march=armv7-m' '-dumpdir' 'CMakeFiles/cmTC_0f3f6.dir/'
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/as -v -march=armv7-m -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj /var/folders/lm/tmxn7r012t1dv_9flxb_mv_40000gn/T//ccRdZ9rB.s
        GNU assembler version 2.42.0 (arm-none-eabi) using BFD version (GNU Tools for STM32 13.3.rel1.20240926-1715) 2.42.0.20240614
        COMPILER_PATH=/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7-m/nofp/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/lib/thumb/v7-m/nofp/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m3' '-Wall' '-fdata-sections' '-ffunction-sections' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv7-m' '-march=armv7-m' '-dumpdir' 'CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.'
        Linking C static library libcmTC_0f3f6.a
        /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -P CMakeFiles/cmTC_0f3f6.dir/cmake_clean_target.cmake
        /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -E cmake_link_script CMakeFiles/cmTC_0f3f6.dir/link.txt --verbose=1
        /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-ar qc libcmTC_0f3f6.a CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj
        /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-ranlib libcmTC_0f3f6.a
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:28 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include]
          add: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include-fixed]
          add: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include]
        end of search list found
        collapse include dir [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include] ==> [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include]
        collapse include dir [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include-fixed] ==> [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include-fixed]
        collapse include dir [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include] ==> [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/arm-none-eabi/include]
        implicit include dirs: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include;/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include-fixed;/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/arm-none-eabi/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:159 (message)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:28 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(arm-none-eabi-g\\+\\+|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/CMakeScratch/TryCompile-qSOeDo']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_0f3f6/fast]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_0f3f6.dir/build.make CMakeFiles/cmTC_0f3f6.dir/build]
        ignore line: [Building C object CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj]
        ignore line: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc   -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections  -std=gnu11   -v -o CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj -c /opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc]
        ignore line: [Target: arm-none-eabi]
        ignore line: [Configured with: /build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/src/gcc/configure --target=arm-none-eabi --prefix=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native --libexecdir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/lib --infodir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/info --mandir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/man --htmldir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/html --pdfdir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/pdf --enable-checking=release --enable-languages=c,c++ --enable-plugins --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-newlib --with-headers=yes --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/arm-none-eabi --with-zstd=no --build=x86_64-apple-darwin10 --host=x86_64-apple-darwin10 --with-gmp=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-mpfr=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-mpc=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-isl=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-lstdc++ -lm' --with-pkgversion='GNU Tools for STM32 13.3.rel1.20240926-1715' --with-multilib-list=rmprofile,aprofile]
        ignore line: [Thread model: single]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 13.3.1 20240614 (GNU Tools for STM32 13.3.rel1.20240926-1715) ]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m3' '-Wall' '-fdata-sections' '-ffunction-sections' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv7-m' '-march=armv7-m' '-dumpdir' 'CMakeFiles/cmTC_0f3f6.dir/']
        ignore line: [ /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/cc1 -quiet -v -imultilib thumb/v7-m/nofp -iprefix /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/ -isysroot /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi -D__USES_INITFINI__ /opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_0f3f6.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mcpu=cortex-m3 -mfloat-abi=soft -mthumb -mlibarch=armv7-m -march=armv7-m -Wall -std=gnu11 -version -fdata-sections -ffunction-sections -o /var/folders/lm/tmxn7r012t1dv_9flxb_mv_40000gn/T//ccRdZ9rB.s]
        ignore line: [GNU C11 (GNU Tools for STM32 13.3.rel1.20240926-1715) version 13.3.1 20240614 (arm-none-eabi)]
        ignore line: [	compiled by GNU C version 4.2.1 Compatible Apple LLVM 10.0.1 (clang-1001.0.46.4)  GMP version 6.2.1  MPFR version 3.1.6  MPC version 1.0.3  isl version isl-0.15-1-g835ea3a-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/include"]
        ignore line: [ignoring nonexistent directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/usr/local/include"]
        ignore line: [ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/include-fixed"]
        ignore line: [ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include"]
        ignore line: [ignoring nonexistent directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/usr/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include]
        ignore line: [ /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include-fixed]
        ignore line: [ /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 9f5afc2b4ae1532267b7b37af76b05d0]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m3' '-Wall' '-fdata-sections' '-ffunction-sections' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv7-m' '-march=armv7-m' '-dumpdir' 'CMakeFiles/cmTC_0f3f6.dir/']
        ignore line: [ /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/as -v -march=armv7-m -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj /var/folders/lm/tmxn7r012t1dv_9flxb_mv_40000gn/T//ccRdZ9rB.s]
        ignore line: [GNU assembler version 2.42.0 (arm-none-eabi) using BFD version (GNU Tools for STM32 13.3.rel1.20240926-1715) 2.42.0.20240614]
        ignore line: [COMPILER_PATH=/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/]
        ignore line: [LIBRARY_PATH=/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7-m/nofp/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/lib/thumb/v7-m/nofp/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/lib/]
        ignore line: [COLLECT_GCC_OPTIONS='-mcpu=cortex-m3' '-Wall' '-fdata-sections' '-ffunction-sections' '-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv7-m' '-march=armv7-m' '-dumpdir' 'CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.']
        ignore line: [Linking C static library libcmTC_0f3f6.a]
        ignore line: [/opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -P CMakeFiles/cmTC_0f3f6.dir/cmake_clean_target.cmake]
        ignore line: [/opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -E cmake_link_script CMakeFiles/cmTC_0f3f6.dir/link.txt --verbose=1]
        ignore line: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-ar qc libcmTC_0f3f6.a CMakeFiles/cmTC_0f3f6.dir/CMakeCCompilerABI.c.obj]
        ignore line: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-ranlib libcmTC_0f3f6.a]
        ignore line: []
        ignore line: []
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:28 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/CMakeScratch/TryCompile-ieqpvU"
      binary: "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/CMakeScratch/TryCompile-ieqpvU"
    cmakeVariables:
      CMAKE_CXX_FLAGS: " -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -fno-rtti -fno-exceptions -fno-threadsafe-statics"
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g3"
      CMAKE_EXE_LINKER_FLAGS: "-mcpu=cortex-m3  -T \"/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/STM32F103XX_FLASH.ld\" --specs=nano.specs -Wl,-Map=SPI_Screen001.map -Wl,--gc-sections -Wl,--print-memory-usage"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/CMakeScratch/TryCompile-ieqpvU'
        
        Run Build Command(s): /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_b000a/fast
        /Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_b000a.dir/build.make CMakeFiles/cmTC_b000a.dir/build
        Building CXX object CMakeFiles/cmTC_b000a.dir/CMakeCXXCompilerABI.cpp.obj
        /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-g++   -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -fno-rtti -fno-exceptions -fno-threadsafe-statics    -v -o CMakeFiles/cmTC_b000a.dir/CMakeCXXCompilerABI.cpp.obj -c /opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-g++
        Target: arm-none-eabi
        Configured with: /build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/src/gcc/configure --target=arm-none-eabi --prefix=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native --libexecdir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/lib --infodir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/info --mandir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/man --htmldir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/html --pdfdir=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/share/doc/gcc-arm-none-eabi/pdf --enable-checking=release --enable-languages=c,c++ --enable-plugins --disable-decimal-float --disable-libffi --disable-libgomp --disable-libmudflap --disable-libquadmath --disable-libssp --disable-libstdcxx-pch --disable-nls --disable-shared --disable-threads --disable-tls --with-gnu-as --with-gnu-ld --with-newlib --with-headers=yes --with-python-dir=share/gcc-arm-none-eabi --with-sysroot=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/install-native/arm-none-eabi --with-zstd=no --build=x86_64-apple-darwin10 --host=x86_64-apple-darwin10 --with-gmp=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-mpfr=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-mpc=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-isl=/build/gnu-tools-for-stm32_13.3.rel1.20240926-1715/build-native/host-libs/usr --with-host-libstdcxx='-static-libgcc -Wl,-lstdc++ -lm' --with-pkgversion='GNU Tools for STM32 13.3.rel1.20240926-1715' --with-multilib-list=rmprofile,aprofile
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 13.3.1 20240614 (GNU Tools for STM32 13.3.rel1.20240926-1715) 
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m3' '-Wall' '-fdata-sections' '-ffunction-sections' '-fno-rtti' '-fno-exceptions' '-fno-threadsafe-statics' '-v' '-o' 'CMakeFiles/cmTC_b000a.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv7-m' '-march=armv7-m' '-dumpdir' 'CMakeFiles/cmTC_b000a.dir/'
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/cc1plus -quiet -v -imultilib thumb/v7-m/nofp -iprefix /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/ -isysroot /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi -D__USES_INITFINI__ /opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_b000a.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mcpu=cortex-m3 -mfloat-abi=soft -mthumb -mlibarch=armv7-m -march=armv7-m -Wall -version -fdata-sections -ffunction-sections -fno-rtti -fno-exceptions -fno-threadsafe-statics -o /var/folders/lm/tmxn7r012t1dv_9flxb_mv_40000gn/T//cct3HpAF.s
        GNU C++17 (GNU Tools for STM32 13.3.rel1.20240926-1715) version 13.3.1 20240614 (arm-none-eabi)
        	compiled by GNU C version 4.2.1 Compatible Apple LLVM 10.0.1 (clang-1001.0.46.4), GMP version 6.2.1, MPFR version 3.1.6, MPC version 1.0.3, isl version isl-0.15-1-g835ea3a-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1"
        ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1/arm-none-eabi/thumb/v7-m/nofp"
        ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1/backward"
        ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/include"
        ignoring nonexistent directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/usr/local/include"
        ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/include-fixed"
        ignoring duplicate directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/../../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include"
        ignoring nonexistent directory "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/usr/include"
        #include "..." search starts here:
        #include <...> search starts here:
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1/arm-none-eabi/thumb/v7-m/nofp
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1/backward
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include-fixed
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include
        End of search list.
        Compiler executable checksum: b1977bec7a6a213e4ed9949033bce3af
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m3' '-Wall' '-fdata-sections' '-ffunction-sections' '-fno-rtti' '-fno-exceptions' '-fno-threadsafe-statics' '-v' '-o' 'CMakeFiles/cmTC_b000a.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv7-m' '-march=armv7-m' '-dumpdir' 'CMakeFiles/cmTC_b000a.dir/'
         /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/as -v -march=armv7-m -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_b000a.dir/CMakeCXXCompilerABI.cpp.obj /var/folders/lm/tmxn7r012t1dv_9flxb_mv_40000gn/T//cct3HpAF.s
        GNU assembler version 2.42.0 (arm-none-eabi) using BFD version (GNU Tools for STM32 13.3.rel1.20240926-1715) 2.42.0.20240614
        COMPILER_PATH=/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7-m/nofp/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7-m/nofp/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/lib/thumb/v7-m/nofp/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/:/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-mcpu=cortex-m3' '-Wall' '-fdata-sections' '-ffunction-sections' '-fno-rtti' '-fno-exceptions' '-fno-threadsafe-statics' '-v' '-o' 'CMakeFiles/cmTC_b000a.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mfloat-abi=soft' '-mthumb' '-mlibarch=armv7-m' '-march=armv7-m' '-dumpdir' 'CMakeFiles/cmTC_b000a.dir/CMakeCXXCompilerABI.cpp.'
        Linking CXX static library libcmTC_b000a.a
        /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -P CMakeFiles/cmTC_b000a.dir/cmake_clean_target.cmake
        /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b000a.dir/link.txt --verbose=1
        /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-ar qc libcmTC_b000a.a CMakeFiles/cmTC_b000a.dir/CMakeCXXCompilerABI.cpp.obj
        /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-ranlib libcmTC_b000a.a
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:127 (message)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:28 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1]
          add: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1/arm-none-eabi/thumb/v7-m/nofp]
          add: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1/backward]
          add: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include]
          add: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include-fixed]
          add: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include]
        end of search list found
        collapse include dir [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1] ==> [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/arm-none-eabi/include/c++/13.3.1]
        collapse include dir [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1/arm-none-eabi/thumb/v7-m/nofp] ==> [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/arm-none-eabi/include/c++/13.3.1/arm-none-eabi/thumb/v7-m/nofp]
        collapse include dir [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include/c++/13.3.1/backward] ==> [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/arm-none-eabi/include/c++/13.3.1/backward]
        collapse include dir [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include] ==> [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include]
        collapse include dir [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/include-fixed] ==> [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include-fixed]
        collapse include dir [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/include] ==> [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/arm-none-eabi/include]
        implicit include dirs: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/arm-none-eabi/include/c++/13.3.1;/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/arm-none-eabi/include/c++/13.3.1/arm-none-eabi/thumb/v7-m/nofp;/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/arm-none-eabi/include/c++/13.3.1/backward;/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include;/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/lib/gcc/arm-none-eabi/13.3.1/include-fixed;/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/arm-none-eabi/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:159 (message)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:28 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(arm-none-eabi-g\\+\\+|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        ignore line: [Change Dir: '/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles/CMakeScratch/TryCompile-ieqpvU']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_b000a/fast]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_b000a.dir/build.make CMakeFiles/cmTC_b000a.dir/build]
        ignore line: [Building CXX object CMakeFiles/cmTC_b000a.dir/CMakeCXXCompilerABI.cpp.obj]
        link line: [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-g++   -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -fno-rtti -fno-exceptions -fno-threadsafe-statics    -v -o CMakeFiles/cmTC_b000a.dir/CMakeCXXCompilerABI.cpp.obj -c /opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp]
          arg [/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-g++] ==> ignore
          arg [-mcpu=cortex-m3] ==> ignore
          arg [-Wall] ==> ignore
          arg [-fdata-sections] ==> ignore
          arg [-ffunction-sections] ==> ignore
          arg [-mcpu=cortex-m3] ==> ignore
          arg [-Wall] ==> ignore
          arg [-fdata-sections] ==> ignore
          arg [-ffunction-sections] ==> ignore
          arg [-fno-rtti] ==> ignore
          arg [-fno-exceptions] ==> ignore
          arg [-fno-threadsafe-statics] ==> ignore
          arg [-v] ==> ignore
          arg [-o] ==> ignore
          arg [CMakeFiles/cmTC_b000a.dir/CMakeCXXCompilerABI.cpp.obj] ==> ignore
          arg [-c] ==> ignore
          arg [/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeCXXCompilerABI.cpp] ==> ignore
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:1131 (message)"
      - "/opt/ST/STM32CubeCLT_1.19.0/CMake/share/cmake-3.28/Modules/CMakeDetermineASMCompiler.cmake:131 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "CMakeLists.txt:32 (enable_language)"
    message: |
      Checking whether the ASM compiler is GNU using "--version" matched "(GNU assembler)|(GCC)|(Free Software Foundation)":
      arm-none-eabi-gcc (GNU Tools for STM32 13.3.rel1.20240926-1715) 13.3.1 20240614
      Copyright (C) 2023 Free Software Foundation, Inc.
      This is free software; see the source for copying conditions.  There is NO
      warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
      
...
