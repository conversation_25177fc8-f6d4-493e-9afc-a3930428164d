set(CMAKE_HOST_SYSTEM "Darwin-23.2.0")
set(CMAKE_HOST_SYSTEM_NAME "Darwin")
set(CMAKE_HOST_SYSTEM_VERSION "23.2.0")
set(CMAKE_HOST_SYSTEM_PROCESSOR "arm64")

include("/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/gcc-arm-none-eabi.cmake")

set(CMAKE_SYSTEM "Generic")
set(CMAKE_SYSTEM_NAME "Generic")
set(CMAKE_SYSTEM_VERSION "")
set(CMAKE_SYSTEM_PROCESSOR "arm")

set(CMAKE_CROSSCOMPILING "TRUE")

set(CMAKE_SYSTEM_LOADED 1)
