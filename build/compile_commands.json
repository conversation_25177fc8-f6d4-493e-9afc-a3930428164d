[{"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/main.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/main.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_it.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_it.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_hal_msp.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/stm32f1xx_hal_msp.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/sysmem.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/sysmem.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/syscalls.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/syscalls.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -x assembler-with-cpp -MMD -MP -g -o CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/startup_stm32f103xb.s", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/startup_stm32f103xb.s", "output": "CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735_test.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/z_displ_ST7735_test.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font8.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font8.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font12.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font12.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font16.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font16.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font20.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font20.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font24.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/font24.c", "output": "CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32f1xx.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/system_stm32f1xx.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Core/Src/system_stm32f1xx.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32f1xx.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c.obj"}, {"directory": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/cmake/stm32cubemx", "command": "/opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c.obj -c /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c", "file": "/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c", "output": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c.obj"}]