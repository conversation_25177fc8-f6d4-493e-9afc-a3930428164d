# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# compile C with /opt/ST/STM32CubeCLT_1.19.0/GNU-tools-for-STM32/bin/arm-none-eabi-gcc
C_DEFINES = -DDEBUG -DSTM32F103xB -DUSE_HAL_DRIVER

C_INCLUDES = -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Core/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/STM32F1xx_HAL_Driver/Inc/Legacy -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32F1xx/Include -I/Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/cmake/stm32cubemx/../../Drivers/CMSIS/Include

C_FLAGS =  -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -mcpu=cortex-m3  -Wall -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11

