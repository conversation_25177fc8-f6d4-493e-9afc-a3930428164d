# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake

# The command to remove a file.
RM = /opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/ST/STM32CubeCLT_1.19.0/CMake/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/ST/STM32CubeCLT_1.19.0/CMake/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/Dev/STM32CubeIDE/workspace_1.19.0/SPI_Screen001/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named SPI_Screen001

# Build rule for target.
SPI_Screen001: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 SPI_Screen001
.PHONY : SPI_Screen001

# fast build rule for target.
SPI_Screen001/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/build
.PHONY : SPI_Screen001/fast

#=============================================================================
# Target rules for targets named STM32_Drivers

# Build rule for target.
STM32_Drivers: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 STM32_Drivers
.PHONY : STM32_Drivers

# fast build rule for target.
STM32_Drivers/fast:
	$(MAKE) $(MAKESILENT) -f cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/build.make cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/build
.PHONY : STM32_Drivers/fast

Core/Src/font12.obj: Core/Src/font12.c.obj
.PHONY : Core/Src/font12.obj

# target to build an object file
Core/Src/font12.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.obj
.PHONY : Core/Src/font12.c.obj

Core/Src/font12.i: Core/Src/font12.c.i
.PHONY : Core/Src/font12.i

# target to preprocess a source file
Core/Src/font12.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.i
.PHONY : Core/Src/font12.c.i

Core/Src/font12.s: Core/Src/font12.c.s
.PHONY : Core/Src/font12.s

# target to generate assembly for a file
Core/Src/font12.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font12.c.s
.PHONY : Core/Src/font12.c.s

Core/Src/font16.obj: Core/Src/font16.c.obj
.PHONY : Core/Src/font16.obj

# target to build an object file
Core/Src/font16.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.obj
.PHONY : Core/Src/font16.c.obj

Core/Src/font16.i: Core/Src/font16.c.i
.PHONY : Core/Src/font16.i

# target to preprocess a source file
Core/Src/font16.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.i
.PHONY : Core/Src/font16.c.i

Core/Src/font16.s: Core/Src/font16.c.s
.PHONY : Core/Src/font16.s

# target to generate assembly for a file
Core/Src/font16.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font16.c.s
.PHONY : Core/Src/font16.c.s

Core/Src/font20.obj: Core/Src/font20.c.obj
.PHONY : Core/Src/font20.obj

# target to build an object file
Core/Src/font20.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.obj
.PHONY : Core/Src/font20.c.obj

Core/Src/font20.i: Core/Src/font20.c.i
.PHONY : Core/Src/font20.i

# target to preprocess a source file
Core/Src/font20.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.i
.PHONY : Core/Src/font20.c.i

Core/Src/font20.s: Core/Src/font20.c.s
.PHONY : Core/Src/font20.s

# target to generate assembly for a file
Core/Src/font20.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font20.c.s
.PHONY : Core/Src/font20.c.s

Core/Src/font24.obj: Core/Src/font24.c.obj
.PHONY : Core/Src/font24.obj

# target to build an object file
Core/Src/font24.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.obj
.PHONY : Core/Src/font24.c.obj

Core/Src/font24.i: Core/Src/font24.c.i
.PHONY : Core/Src/font24.i

# target to preprocess a source file
Core/Src/font24.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.i
.PHONY : Core/Src/font24.c.i

Core/Src/font24.s: Core/Src/font24.c.s
.PHONY : Core/Src/font24.s

# target to generate assembly for a file
Core/Src/font24.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font24.c.s
.PHONY : Core/Src/font24.c.s

Core/Src/font8.obj: Core/Src/font8.c.obj
.PHONY : Core/Src/font8.obj

# target to build an object file
Core/Src/font8.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.obj
.PHONY : Core/Src/font8.c.obj

Core/Src/font8.i: Core/Src/font8.c.i
.PHONY : Core/Src/font8.i

# target to preprocess a source file
Core/Src/font8.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.i
.PHONY : Core/Src/font8.c.i

Core/Src/font8.s: Core/Src/font8.c.s
.PHONY : Core/Src/font8.s

# target to generate assembly for a file
Core/Src/font8.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/font8.c.s
.PHONY : Core/Src/font8.c.s

Core/Src/main.obj: Core/Src/main.c.obj
.PHONY : Core/Src/main.obj

# target to build an object file
Core/Src/main.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.obj
.PHONY : Core/Src/main.c.obj

Core/Src/main.i: Core/Src/main.c.i
.PHONY : Core/Src/main.i

# target to preprocess a source file
Core/Src/main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.i
.PHONY : Core/Src/main.c.i

Core/Src/main.s: Core/Src/main.c.s
.PHONY : Core/Src/main.s

# target to generate assembly for a file
Core/Src/main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/main.c.s
.PHONY : Core/Src/main.c.s

Core/Src/stm32f1xx_hal_msp.obj: Core/Src/stm32f1xx_hal_msp.c.obj
.PHONY : Core/Src/stm32f1xx_hal_msp.obj

# target to build an object file
Core/Src/stm32f1xx_hal_msp.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.obj
.PHONY : Core/Src/stm32f1xx_hal_msp.c.obj

Core/Src/stm32f1xx_hal_msp.i: Core/Src/stm32f1xx_hal_msp.c.i
.PHONY : Core/Src/stm32f1xx_hal_msp.i

# target to preprocess a source file
Core/Src/stm32f1xx_hal_msp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.i
.PHONY : Core/Src/stm32f1xx_hal_msp.c.i

Core/Src/stm32f1xx_hal_msp.s: Core/Src/stm32f1xx_hal_msp.c.s
.PHONY : Core/Src/stm32f1xx_hal_msp.s

# target to generate assembly for a file
Core/Src/stm32f1xx_hal_msp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_hal_msp.c.s
.PHONY : Core/Src/stm32f1xx_hal_msp.c.s

Core/Src/stm32f1xx_it.obj: Core/Src/stm32f1xx_it.c.obj
.PHONY : Core/Src/stm32f1xx_it.obj

# target to build an object file
Core/Src/stm32f1xx_it.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.obj
.PHONY : Core/Src/stm32f1xx_it.c.obj

Core/Src/stm32f1xx_it.i: Core/Src/stm32f1xx_it.c.i
.PHONY : Core/Src/stm32f1xx_it.i

# target to preprocess a source file
Core/Src/stm32f1xx_it.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.i
.PHONY : Core/Src/stm32f1xx_it.c.i

Core/Src/stm32f1xx_it.s: Core/Src/stm32f1xx_it.c.s
.PHONY : Core/Src/stm32f1xx_it.s

# target to generate assembly for a file
Core/Src/stm32f1xx_it.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/stm32f1xx_it.c.s
.PHONY : Core/Src/stm32f1xx_it.c.s

Core/Src/syscalls.obj: Core/Src/syscalls.c.obj
.PHONY : Core/Src/syscalls.obj

# target to build an object file
Core/Src/syscalls.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.obj
.PHONY : Core/Src/syscalls.c.obj

Core/Src/syscalls.i: Core/Src/syscalls.c.i
.PHONY : Core/Src/syscalls.i

# target to preprocess a source file
Core/Src/syscalls.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.i
.PHONY : Core/Src/syscalls.c.i

Core/Src/syscalls.s: Core/Src/syscalls.c.s
.PHONY : Core/Src/syscalls.s

# target to generate assembly for a file
Core/Src/syscalls.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/syscalls.c.s
.PHONY : Core/Src/syscalls.c.s

Core/Src/sysmem.obj: Core/Src/sysmem.c.obj
.PHONY : Core/Src/sysmem.obj

# target to build an object file
Core/Src/sysmem.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.obj
.PHONY : Core/Src/sysmem.c.obj

Core/Src/sysmem.i: Core/Src/sysmem.c.i
.PHONY : Core/Src/sysmem.i

# target to preprocess a source file
Core/Src/sysmem.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.i
.PHONY : Core/Src/sysmem.c.i

Core/Src/sysmem.s: Core/Src/sysmem.c.s
.PHONY : Core/Src/sysmem.s

# target to generate assembly for a file
Core/Src/sysmem.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/sysmem.c.s
.PHONY : Core/Src/sysmem.c.s

Core/Src/z_displ_ST7735.obj: Core/Src/z_displ_ST7735.c.obj
.PHONY : Core/Src/z_displ_ST7735.obj

# target to build an object file
Core/Src/z_displ_ST7735.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.obj
.PHONY : Core/Src/z_displ_ST7735.c.obj

Core/Src/z_displ_ST7735.i: Core/Src/z_displ_ST7735.c.i
.PHONY : Core/Src/z_displ_ST7735.i

# target to preprocess a source file
Core/Src/z_displ_ST7735.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.i
.PHONY : Core/Src/z_displ_ST7735.c.i

Core/Src/z_displ_ST7735.s: Core/Src/z_displ_ST7735.c.s
.PHONY : Core/Src/z_displ_ST7735.s

# target to generate assembly for a file
Core/Src/z_displ_ST7735.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735.c.s
.PHONY : Core/Src/z_displ_ST7735.c.s

Core/Src/z_displ_ST7735_test.obj: Core/Src/z_displ_ST7735_test.c.obj
.PHONY : Core/Src/z_displ_ST7735_test.obj

# target to build an object file
Core/Src/z_displ_ST7735_test.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.obj
.PHONY : Core/Src/z_displ_ST7735_test.c.obj

Core/Src/z_displ_ST7735_test.i: Core/Src/z_displ_ST7735_test.c.i
.PHONY : Core/Src/z_displ_ST7735_test.i

# target to preprocess a source file
Core/Src/z_displ_ST7735_test.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.i
.PHONY : Core/Src/z_displ_ST7735_test.c.i

Core/Src/z_displ_ST7735_test.s: Core/Src/z_displ_ST7735_test.c.s
.PHONY : Core/Src/z_displ_ST7735_test.s

# target to generate assembly for a file
Core/Src/z_displ_ST7735_test.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/Core/Src/z_displ_ST7735_test.c.s
.PHONY : Core/Src/z_displ_ST7735_test.c.s

startup_stm32f103xb.obj: startup_stm32f103xb.s.obj
.PHONY : startup_stm32f103xb.obj

# target to build an object file
startup_stm32f103xb.s.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/SPI_Screen001.dir/build.make CMakeFiles/SPI_Screen001.dir/startup_stm32f103xb.s.obj
.PHONY : startup_stm32f103xb.s.obj

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... SPI_Screen001"
	@echo "... STM32_Drivers"
	@echo "... Core/Src/font12.obj"
	@echo "... Core/Src/font12.i"
	@echo "... Core/Src/font12.s"
	@echo "... Core/Src/font16.obj"
	@echo "... Core/Src/font16.i"
	@echo "... Core/Src/font16.s"
	@echo "... Core/Src/font20.obj"
	@echo "... Core/Src/font20.i"
	@echo "... Core/Src/font20.s"
	@echo "... Core/Src/font24.obj"
	@echo "... Core/Src/font24.i"
	@echo "... Core/Src/font24.s"
	@echo "... Core/Src/font8.obj"
	@echo "... Core/Src/font8.i"
	@echo "... Core/Src/font8.s"
	@echo "... Core/Src/main.obj"
	@echo "... Core/Src/main.i"
	@echo "... Core/Src/main.s"
	@echo "... Core/Src/stm32f1xx_hal_msp.obj"
	@echo "... Core/Src/stm32f1xx_hal_msp.i"
	@echo "... Core/Src/stm32f1xx_hal_msp.s"
	@echo "... Core/Src/stm32f1xx_it.obj"
	@echo "... Core/Src/stm32f1xx_it.i"
	@echo "... Core/Src/stm32f1xx_it.s"
	@echo "... Core/Src/syscalls.obj"
	@echo "... Core/Src/syscalls.i"
	@echo "... Core/Src/syscalls.s"
	@echo "... Core/Src/sysmem.obj"
	@echo "... Core/Src/sysmem.i"
	@echo "... Core/Src/sysmem.s"
	@echo "... Core/Src/z_displ_ST7735.obj"
	@echo "... Core/Src/z_displ_ST7735.i"
	@echo "... Core/Src/z_displ_ST7735.s"
	@echo "... Core/Src/z_displ_ST7735_test.obj"
	@echo "... Core/Src/z_displ_ST7735_test.i"
	@echo "... Core/Src/z_displ_ST7735_test.s"
	@echo "... startup_stm32f103xb.obj"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

